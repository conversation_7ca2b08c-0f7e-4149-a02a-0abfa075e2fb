const { app } = require('@azure/functions');
const Database = require('../../shared/database');
const AuthService = require('../../shared/auth');
const logger = require('../../shared/logger');
const FunctionMiddleware = require('../../shared/functionMiddleware');

// Login handler
const loginHandler = async (request, context) => {
    const db = new Database();
    const auth = new AuthService();

    const body = await request.json();
    const { email, password } = body;

    if (!email || !password) {
        logger.warn('Login attempt with missing credentials', {
            email: email ? '[PROVIDED]' : '[MISSING]',
            password: password ? '[PROVIDED]' : '[MISSING]'
        }, context);

        throw new Error('Email and password are required');
    }

    // Get user by email
    const user = await db.getUserByEmail(email);
    if (!user) {
        logger.logSecurityEvent('Login Failed - User Not Found', null,
            request.headers?.['x-forwarded-for'], { email }, context);
        throw new Error('Invalid credentials');
    }

    // Verify password
    const isValidPassword = await auth.comparePassword(password, user.password_hash);
    if (!isValidPassword) {
        logger.logSecurityEvent('Login Failed - Invalid Password', user.id,
            request.headers?.['x-forwarded-for'], { email }, context);
        throw new Error('Invalid credentials');
    }

    // Generate token
    const token = auth.generateToken(user);

    // Log successful login
    logger.logUserAction(user.id, 'login', 'authentication', {
        email: user.email,
        role: user.role,
        ipAddress: request.headers?.['x-forwarded-for']
    }, context);

    return {
        status: 200,
        jsonBody: {
            message: 'Login successful',
            user: auth.sanitizeUser(user),
            token
        }
    };
};

// Register handler
const registerHandler = async (request, context) => {
    const db = new Database();
    const auth = new AuthService();

    const body = await request.json();
    const { email, password, firstName, lastName, role = 'user' } = body;

    if (!email || !password || !firstName || !lastName) {
        logger.warn('Registration attempt with missing fields', {
            email: email ? '[PROVIDED]' : '[MISSING]',
            password: password ? '[PROVIDED]' : '[MISSING]',
            firstName: firstName ? '[PROVIDED]' : '[MISSING]',
            lastName: lastName ? '[PROVIDED]' : '[MISSING]'
        }, context);

        throw new Error('All fields are required');
    }

    // Check if user already exists
    const existingUser = await db.getUserByEmail(email);
    if (existingUser) {
        logger.logSecurityEvent('Registration Failed - Email Exists', null,
            request.headers?.['x-forwarded-for'], { email }, context);
        throw new Error('User with this email already exists');
    }

    // Hash password
    const passwordHash = await auth.hashPassword(password);

    // Create user
    const newUser = await db.createUser({
        email,
        password_hash: passwordHash,
        first_name: firstName,
        last_name: lastName,
        role
    });

    // Generate token
    const token = auth.generateToken(newUser);

    // Log successful registration
    logger.logUserAction(newUser.id, 'register', 'authentication', {
        email: newUser.email,
        role: newUser.role,
        ipAddress: request.headers?.['x-forwarded-for']
    }, context);

    return {
        status: 201,
        jsonBody: {
            message: 'Registration successful',
            user: auth.sanitizeUser(newUser),
            token
        }
    };
};

// Validation schemas
const loginValidation = {
    body: {
        required: ['email', 'password']
    }
};

const registerValidation = {
    body: {
        required: ['email', 'password', 'firstName', 'lastName']
    }
};

// Register login endpoint
app.http('login', {
    methods: ['POST'],
    authLevel: 'anonymous',
    handler: FunctionMiddleware.compose(
        FunctionMiddleware.withValidation(loginValidation),
        FunctionMiddleware.withRateLimit({ maxRequests: 10, windowMs: 60000 })
    )(loginHandler)
});

// Register registration endpoint
app.http('register', {
    methods: ['POST'],
    authLevel: 'anonymous',
    handler: FunctionMiddleware.compose(
        FunctionMiddleware.withValidation(registerValidation),
        FunctionMiddleware.withRateLimit({ maxRequests: 5, windowMs: 60000 })
    )(registerHandler)
});

// Token verification endpoint
app.http('verify', {
    methods: ['GET'],
    authLevel: 'anonymous',
    handler: FunctionMiddleware.withAuth(async (request, context) => {
        // User is already verified by middleware
        const user = request.user;

        logger.info('Token verification successful', {
            userId: user.id,
            email: user.email
        }, context);

        return {
            status: 200,
            jsonBody: {
                user: {
                    id: user.id,
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    role: user.role
                }
            }
        };
    })
});
