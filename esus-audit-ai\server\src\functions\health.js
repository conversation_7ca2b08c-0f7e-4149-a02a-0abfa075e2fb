const { app } = require('@azure/functions');
const logger = require('../../shared/logger');
const monitoring = require('../../shared/monitoring');
const Database = require('../../shared/database');
const FunctionMiddleware = require('../../shared/functionMiddleware');

// Enhanced health check with comprehensive system monitoring
const healthHandler = async (request, context) => {
    const startTime = Date.now();
    const checks = {};
    let overallStatus = 'healthy';

    try {
        // Database health check
        try {
            const db = new Database();
            const dbStart = Date.now();
            await db.query('SELECT 1 as health_check');
            const dbDuration = Date.now() - dbStart;

            checks.database = {
                status: 'healthy',
                responseTime: dbDuration,
                details: 'Database connection successful'
            };

            logger.logHealthCheck('database', 'healthy', { responseTime: dbDuration }, context);
        } catch (dbError) {
            checks.database = {
                status: 'unhealthy',
                error: dbError.message,
                details: 'Database connection failed'
            };
            overallStatus = 'degraded';
            logger.logHealthCheck('database', 'unhealthy', { error: dbError.message }, context);
        }

        // Memory health check
        const memoryUsage = process.memoryUsage();
        const memoryUsedMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
        const memoryTotalMB = Math.round(memoryUsage.heapTotal / 1024 / 1024);
        const memoryUsagePercent = (memoryUsedMB / memoryTotalMB) * 100;

        checks.memory = {
            status: memoryUsagePercent > 90 ? 'unhealthy' : memoryUsagePercent > 75 ? 'warning' : 'healthy',
            usage: {
                used: `${memoryUsedMB}MB`,
                total: `${memoryTotalMB}MB`,
                percentage: `${memoryUsagePercent.toFixed(1)}%`
            },
            details: memoryUsage
        };

        if (checks.memory.status === 'unhealthy') {
            overallStatus = 'unhealthy';
        } else if (checks.memory.status === 'warning' && overallStatus === 'healthy') {
            overallStatus = 'degraded';
        }

        // Application Insights health check
        checks.monitoring = {
            status: monitoring.isEnabled ? 'healthy' : 'warning',
            enabled: monitoring.isEnabled,
            details: monitoring.isEnabled ? 'Application Insights active' : 'Application Insights not configured'
        };

        // Azure Services health check (basic connectivity)
        try {
            // Check if required environment variables are present
            const requiredEnvVars = [
                'AZURE_STORAGE_CONNECTION_STRING',
                'AZURE_FORM_RECOGNIZER_ENDPOINT',
                'AZURE_OPENAI_ENDPOINT',
                'DATABASE_URL'
            ];

            const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

            checks.configuration = {
                status: missingVars.length === 0 ? 'healthy' : 'unhealthy',
                missingVariables: missingVars,
                details: missingVars.length === 0 ? 'All required configuration present' : `Missing: ${missingVars.join(', ')}`
            };

            if (missingVars.length > 0) {
                overallStatus = 'unhealthy';
            }
        } catch (configError) {
            checks.configuration = {
                status: 'unhealthy',
                error: configError.message,
                details: 'Configuration check failed'
            };
            overallStatus = 'unhealthy';
        }

        const responseTime = Date.now() - startTime;

        const healthStatus = {
            status: overallStatus,
            timestamp: new Date().toISOString(),
            version: process.env.npm_package_version || '1.0.0',
            environment: process.env.NODE_ENV || 'development',
            functionName: context.functionName,
            invocationId: context.invocationId,
            uptime: process.uptime(),
            responseTime,
            checks
        };

        // Log health check result
        logger.info(`Health check completed: ${overallStatus}`, {
            status: overallStatus,
            responseTime,
            checksCount: Object.keys(checks).length
        }, context);

        // Track health metrics
        monitoring.trackEvent('HealthCheck', {
            status: overallStatus,
            functionName: context.functionName,
            environment: process.env.NODE_ENV || 'development'
        }, {
            responseTime,
            memoryUsagePercent,
            checksCount: Object.keys(checks).length
        });

        const statusCode = overallStatus === 'healthy' ? 200 : overallStatus === 'degraded' ? 200 : 503;

        return {
            status: statusCode,
            jsonBody: healthStatus,
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate'
            }
        };

    } catch (error) {
        const responseTime = Date.now() - startTime;

        logger.error('Health check failed', error, {
            responseTime,
            functionName: context.functionName
        }, context);

        return {
            status: 503,
            jsonBody: {
                status: 'unhealthy',
                timestamp: new Date().toISOString(),
                error: 'Health check failed',
                message: error.message,
                responseTime,
                functionName: context.functionName,
                invocationId: context.invocationId
            },
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate'
            }
        };
    }
};

// Register the health function with middleware
app.http('health', {
    methods: ['GET'],
    authLevel: 'anonymous',
    handler: FunctionMiddleware.withErrorHandling(healthHandler)
});
