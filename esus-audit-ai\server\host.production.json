{"version": "2.0", "logging": {"applicationInsights": {"samplingSettings": {"isEnabled": true, "maxTelemetryItemsPerSecond": 50, "excludedTypes": "Request"}, "enableLiveMetricsFilters": true, "httpAutoCollectionOptions": {"enableHttpTriggerExtendedInfoCollection": true, "enableW3CDistributedTracing": true, "enableResponseHeaderInjection": true}, "snapshotConfiguration": {"agentEndpoint": "", "captureSnapshotMemoryWeight": 0.5, "failedRequestThrottlingSettings": {"maxTelemetryItemsPerSecond": 3, "samplingPercentage": 100}, "handleUntrackedExceptions": true, "isEnabled": true, "isEnabledInDeveloperMode": false, "isEnabledWhenProfiling": true, "isExceptionSnappointsEnabled": false, "isLowPrioritySnapshotUploader": true, "maximumCollectionPlanSize": 50, "maximumSnapshotsRequired": 3, "problemCounterResetInterval": "24:00:00", "provideAnonymousTelemetry": true, "reconnectInterval": "00:15:00", "shadowCopyFolder": "", "shareUploaderProcess": true, "snapshotInLowPriorityThread": true, "snapshotsPerDayLimit": 30, "snapshotsPerTenMinutesLimit": 1, "tempFolder": "", "thresholdForSnapshotting": 1, "uploaderProxy": ""}}, "logLevel": {"default": "Information", "Host.Results": "Information", "Function": "Information", "Host.Aggregator": "Information", "Microsoft": "Warning", "System": "Warning"}, "console": {"isEnabled": false}, "fileLoggingMode": "always"}, "extensionBundle": {"id": "Microsoft.Azure.Functions.ExtensionBundle", "version": "[4.*, 5.0.0)"}, "functionTimeout": "00:10:00", "healthMonitor": {"enabled": true, "healthCheckInterval": "00:00:10", "healthCheckWindow": "00:02:00", "healthCheckThreshold": 6, "counterThreshold": 0.8}, "http": {"routePrefix": "api", "maxOutstandingRequests": 500, "maxConcurrentRequests": 200, "dynamicThrottlesEnabled": true, "hsts": {"isEnabled": true, "maxAge": "365"}}, "retry": {"strategy": "exponentialBackoff", "maxRetryCount": 3, "minimumInterval": "00:00:02", "maximumInterval": "00:00:30"}, "cors": {"allowedOrigins": ["https://esus-audit-ai.com", "https://app.esus-audit-ai.com"], "allowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "allowedHeaders": ["Content-Type", "Authorization", "X-Correlation-ID"], "allowCredentials": true}, "watchDirectories": [], "managedDependency": {"enabled": true}}