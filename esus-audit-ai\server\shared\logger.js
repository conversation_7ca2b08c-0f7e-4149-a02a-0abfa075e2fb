// Enhanced structured logging service for Azure Functions
const monitoring = require('./monitoring');
const { v4: uuidv4 } = require('uuid');

class Logger {
    constructor() {
        this.logLevel = process.env.LOG_LEVEL || 'info';
        this.levels = {
            error: 0,
            warn: 1,
            info: 2,
            debug: 3
        };
        this.serviceName = 'esus-audit-ai';
        this.version = process.env.npm_package_version || '1.0.0';
    }

    shouldLog(level) {
        return this.levels[level] <= this.levels[this.logLevel];
    }

    formatMessage(level, message, context = {}, azureContext = null) {
        const timestamp = new Date().toISOString();
        const correlationId = context.correlationId || this.generateCorrelationId();

        const baseLog = {
            timestamp,
            level: level.toUpperCase(),
            service: this.serviceName,
            version: this.version,
            message,
            correlationId,
            environment: process.env.NODE_ENV || 'development',
            ...context
        };

        // Add Azure Functions context information
        if (azureContext) {
            baseLog.functionName = azureContext.functionName;
            baseLog.invocationId = azureContext.invocationId;
            baseLog.executionContext = {
                functionName: azureContext.functionName,
                functionDirectory: azureContext.functionDirectory,
                invocationId: azureContext.invocationId
            };
        }

        return baseLog;
    }

    generateCorrelationId() {
        return uuidv4();
    }

    error(message, error = null, context = {}, azureContext = null) {
        if (!this.shouldLog('error')) return;

        const errorDetails = error ? {
            name: error.name,
            message: error.message,
            stack: error.stack,
            code: error.code,
            statusCode: error.statusCode,
            isOperational: error.isOperational || false
        } : null;

        const logData = this.formatMessage('error', message, {
            ...context,
            error: errorDetails,
            severity: 'High'
        }, azureContext);

        console.error(JSON.stringify(logData));

        // Send to Application Insights with enhanced properties
        if (monitoring.isEnabled) {
            monitoring.trackError(error || new Error(message), {
                ...context,
                functionName: azureContext?.functionName,
                invocationId: azureContext?.invocationId,
                correlationId: logData.correlationId
            });
        }

        return logData.correlationId;
    }

    warn(message, context = {}, azureContext = null) {
        if (!this.shouldLog('warn')) return;

        const logData = this.formatMessage('warn', message, {
            ...context,
            severity: 'Medium'
        }, azureContext);

        console.warn(JSON.stringify(logData));

        if (monitoring.isEnabled) {
            monitoring.trackTrace(message, 'Warning', {
                ...context,
                functionName: azureContext?.functionName,
                invocationId: azureContext?.invocationId,
                correlationId: logData.correlationId
            });
        }

        return logData.correlationId;
    }

    info(message, context = {}, azureContext = null) {
        if (!this.shouldLog('info')) return;

        const logData = this.formatMessage('info', message, {
            ...context,
            severity: 'Low'
        }, azureContext);

        console.log(JSON.stringify(logData));

        if (monitoring.isEnabled) {
            monitoring.trackTrace(message, 'Information', {
                ...context,
                functionName: azureContext?.functionName,
                invocationId: azureContext?.invocationId,
                correlationId: logData.correlationId
            });
        }

        return logData.correlationId;
    }

    debug(message, context = {}, azureContext = null) {
        if (!this.shouldLog('debug')) return;

        const logData = this.formatMessage('debug', message, context, azureContext);
        console.log(JSON.stringify(logData));

        if (monitoring.isEnabled) {
            monitoring.trackTrace(message, 'Verbose', {
                ...context,
                functionName: azureContext?.functionName,
                invocationId: azureContext?.invocationId,
                correlationId: logData.correlationId
            });
        }

        return logData.correlationId;
    }

    // Specialized logging methods for common operations
    logUserAction(userId, action, resource, context = {}) {
        this.info(`User action: ${action}`, {
            userId,
            action,
            resource,
            ...context
        });

        monitoring.trackEvent('UserAction', {
            userId,
            action,
            resource,
            ...context
        });
    }

    logDocumentOperation(operation, documentId, projectId, userId, context = {}) {
        this.info(`Document operation: ${operation}`, {
            operation,
            documentId,
            projectId,
            userId,
            ...context
        });

        monitoring.trackEvent('DocumentOperation', {
            operation,
            documentId,
            projectId,
            userId,
            ...context
        });
    }

    logAIOperation(operation, duration, success, context = {}) {
        this.info(`AI operation: ${operation}`, {
            operation,
            duration,
            success,
            ...context
        });

        monitoring.trackEvent('AIOperation', {
            operation,
            success: success.toString(),
            ...context
        }, {
            duration
        });
    }

    logSecurityEvent(event, userId, ipAddress, context = {}) {
        this.warn(`Security event: ${event}`, {
            event,
            userId,
            ipAddress,
            ...context
        });

        monitoring.trackEvent('SecurityEvent', {
            event,
            userId,
            ipAddress,
            ...context
        });
    }

    logPerformanceMetric(operation, duration, success, context = {}) {
        this.debug(`Performance: ${operation}`, {
            operation,
            duration,
            success,
            ...context
        });

        monitoring.trackPerformanceMetric(`Performance.${operation}`, duration, {
            success: success.toString(),
            ...context
        });
    }

    // Request/Response logging middleware
    logRequest(request, context = {}) {
        const requestData = {
            method: request.method,
            url: request.url,
            userAgent: request.headers?.['user-agent'],
            correlationId: request.headers?.['x-correlation-id'] || this.generateCorrelationId(),
            ...context
        };

        this.info('Request received', requestData);
        return requestData.correlationId;
    }

    logResponse(correlationId, statusCode, duration, context = {}) {
        this.info('Request completed', {
            correlationId,
            statusCode,
            duration,
            ...context
        });

        monitoring.trackPerformanceMetric('Request.Duration', duration, {
            statusCode: statusCode.toString(),
            ...context
        });
    }

    // Database operation logging
    logDatabaseOperation(operation, table, duration, success, context = {}) {
        this.debug(`Database operation: ${operation} on ${table}`, {
            operation,
            table,
            duration,
            success,
            ...context
        });

        monitoring.trackDependency('Database', `${operation} ${table}`, duration, success);
    }

    // Azure service operation logging
    logAzureServiceOperation(service, operation, duration, success, context = {}) {
        this.debug(`Azure ${service}: ${operation}`, {
            service,
            operation,
            duration,
            success,
            ...context
        });

        monitoring.trackDependency(`Azure${service}`, operation, duration, success);
    }

    // Health check logging
    logHealthCheck(component, status, details = {}) {
        const level = status === 'healthy' ? 'info' : 'warn';
        this[level](`Health check: ${component} is ${status}`, {
            component,
            status,
            ...details
        });

        monitoring.trackEvent('HealthCheck', {
            component,
            status,
            ...details
        });
    }
}

module.exports = new Logger();
