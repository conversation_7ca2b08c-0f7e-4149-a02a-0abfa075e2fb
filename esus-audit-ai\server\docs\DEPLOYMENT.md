# Esus Audit AI - Azure Functions Deployment Guide

## Overview

This guide covers the deployment of Esus Audit AI backend services using Azure Functions with enhanced logging, error handling, and CI/CD pipeline.

## Prerequisites

### Required Tools
- [Azure CLI](https://docs.microsoft.com/en-us/cli/azure/install-azure-cli) (latest version)
- [Azure Functions Core Tools](https://docs.microsoft.com/en-us/azure/azure-functions/functions-run-local) v4.x
- [Node.js](https://nodejs.org/) v18.x or later
- [Git](https://git-scm.com/)

### Azure Resources Required
- Azure Function Apps (dev, staging, prod)
- Azure Database for PostgreSQL
- Azure Storage Account
- Azure Form Recognizer
- Azure OpenAI Service
- Azure Cognitive Search
- Azure Service Bus
- Azure Application Insights

## Environment Setup

### 1. Development Environment

```bash
# Clone repository
git clone <repository-url>
cd esus-audit-ai/server

# Install dependencies
npm install

# Copy environment template
cp local.settings.json.example local.settings.json

# Configure local.settings.json with your development values
```

### 2. Azure Function Apps Configuration

Create three Function Apps for different environments:

#### Development
- **Name**: `esus-audit-ai-functions-dev`
- **Runtime**: Node.js 18
- **Plan**: Consumption (Y1)
- **Application Insights**: Enabled

#### Staging
- **Name**: `esus-audit-ai-functions-staging`
- **Runtime**: Node.js 18
- **Plan**: Premium (EP1)
- **Application Insights**: Enabled

#### Production
- **Name**: `esus-audit-ai-functions-prod`
- **Runtime**: Node.js 18
- **Plan**: Premium (EP2)
- **Application Insights**: Enabled

### 3. Environment Variables

Configure the following application settings in each Function App:

#### Required Settings
```
NODE_ENV=production
FUNCTIONS_WORKER_RUNTIME=node
FUNCTIONS_EXTENSION_VERSION=~4

# Database
DATABASE_URL=******************************************/database

# Azure Services
AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;...
AZURE_FORM_RECOGNIZER_ENDPOINT=https://your-form-recognizer.cognitiveservices.azure.com/
AZURE_FORM_RECOGNIZER_KEY=your_key_here
AZURE_OPENAI_ENDPOINT=https://your-openai.openai.azure.com/
AZURE_OPENAI_KEY=your_key_here
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4o-mini
AZURE_SEARCH_ENDPOINT=https://your-search.search.windows.net
AZURE_SEARCH_KEY=your_key_here
AZURE_SERVICE_BUS_CONNECTION_STRING=Endpoint=sb://...

# Application Insights
APPLICATIONINSIGHTS_CONNECTION_STRING=InstrumentationKey=...

# Security
JWT_SECRET=your_secure_jwt_secret_here

# Application Settings
ALLOWED_ORIGINS=https://your-frontend-domain.com
MAX_FILE_SIZE=52428800
ALLOWED_FILE_TYPES=.pdf,.xlsx,.xls,.docx,.doc,.csv
```

## Deployment Methods

### 1. Automated CI/CD (Recommended)

The project includes GitHub Actions workflow for automated deployment:

#### Setup GitHub Secrets
```
AZURE_FUNCTIONAPP_PUBLISH_PROFILE_DEV
AZURE_FUNCTIONAPP_PUBLISH_PROFILE_STAGING
AZURE_FUNCTIONAPP_PUBLISH_PROFILE_PROD
SNYK_TOKEN (optional, for security scanning)
SLACK_WEBHOOK_URL (optional, for notifications)
```

#### Deployment Triggers
- **Development**: Push to `develop` branch
- **Staging**: Push to `staging` branch
- **Production**: Push to `main` branch

### 2. Manual Deployment

#### Using PowerShell Script
```powershell
# Deploy to development
.\scripts\deploy.ps1 -Environment dev

# Deploy to staging
.\scripts\deploy.ps1 -Environment staging

# Deploy to production (requires confirmation)
.\scripts\deploy.ps1 -Environment prod

# Force deployment without confirmation
.\scripts\deploy.ps1 -Environment prod -Force

# Skip tests during deployment
.\scripts\deploy.ps1 -Environment dev -SkipTests
```

#### Using Azure Functions Core Tools
```bash
# Build and deploy
npm run build
func azure functionapp publish esus-audit-ai-functions-prod
```

### 3. Azure CLI Deployment
```bash
# Create deployment package
npm run build
zip -r deployment.zip . -x "node_modules/*" "*.git*" "tests/*"

# Deploy using Azure CLI
az functionapp deployment source config-zip \
  --resource-group esus-audit-ai-prod-rg \
  --name esus-audit-ai-functions-prod \
  --src deployment.zip
```

## Monitoring and Logging

### Application Insights Integration

The application automatically sends telemetry to Application Insights:

- **Request/Response tracking**: All HTTP requests and responses
- **Dependency tracking**: Database, Azure services, external APIs
- **Exception tracking**: Detailed error information with stack traces
- **Custom events**: Business logic events and user actions
- **Performance metrics**: Response times, memory usage, throughput

### Log Levels

Configure logging levels via `LOG_LEVEL` environment variable:
- `error`: Only errors
- `warn`: Warnings and errors
- `info`: Informational messages, warnings, and errors (default)
- `debug`: All messages including debug information

### Health Monitoring

Health check endpoint: `GET /api/health`

Monitors:
- Database connectivity
- Memory usage
- Configuration validity
- Application Insights status

## Security Considerations

### 1. Environment Variables
- Store sensitive values in Azure Key Vault
- Use managed identities where possible
- Rotate secrets regularly

### 2. Network Security
- Configure CORS properly for production
- Use HTTPS only
- Implement rate limiting

### 3. Authentication
- Use strong JWT secrets
- Implement proper token validation
- Log security events

## Troubleshooting

### Common Issues

#### 1. Deployment Failures
```bash
# Check Function App logs
func azure functionapp logstream esus-audit-ai-functions-prod

# Check deployment status
az functionapp deployment list --name esus-audit-ai-functions-prod --resource-group esus-audit-ai-prod-rg
```

#### 2. Configuration Issues
- Verify all required environment variables are set
- Check Application Insights connection string
- Validate database connection string

#### 3. Performance Issues
- Monitor Application Insights for slow requests
- Check memory usage in health endpoint
- Review database query performance

### Rollback Procedure

#### Using Azure Portal
1. Go to Function App → Deployment Center
2. Select previous successful deployment
3. Click "Redeploy"

#### Using Azure CLI
```bash
# List deployments
az functionapp deployment list --name esus-audit-ai-functions-prod --resource-group esus-audit-ai-prod-rg

# Redeploy specific version
az functionapp deployment source config-zip \
  --resource-group esus-audit-ai-prod-rg \
  --name esus-audit-ai-functions-prod \
  --src previous-version.zip
```

## Performance Optimization

### 1. Function App Settings
- Use Premium plan for production
- Enable "Always On" for consistent performance
- Configure appropriate scaling settings

### 2. Code Optimization
- Implement connection pooling for database
- Use async/await properly
- Cache frequently accessed data

### 3. Monitoring
- Set up alerts for high error rates
- Monitor response times
- Track memory usage trends

## Support and Maintenance

### Regular Tasks
- Monitor Application Insights dashboards
- Review error logs weekly
- Update dependencies monthly
- Rotate secrets quarterly

### Alerts Configuration
Set up alerts for:
- High error rates (>5%)
- Slow response times (>5 seconds)
- Memory usage (>80%)
- Failed health checks

For additional support, refer to the main project documentation or contact the development team.
