name: Deploy Azure Functions - <PERSON><PERSON> Audit AI

on:
  push:
    branches:
      - main
      - develop
      - staging
    paths:
      - 'server/**'
  pull_request:
    branches:
      - main
    paths:
      - 'server/**'

env:
  AZURE_FUNCTIONAPP_PACKAGE_PATH: './server'
  NODE_VERSION: '18.x'

jobs:
  # Test and Build Job
  test-and-build:
    runs-on: ubuntu-latest
    steps:
    - name: 'Checkout GitHub Action'
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: server/package-lock.json

    - name: 'Install dependencies'
      run: |
        cd server
        npm ci

    - name: 'Run linting'
      run: |
        cd server
        npm run lint

    - name: 'Run tests'
      run: |
        cd server
        npm run test:coverage

    - name: 'Upload test coverage'
      uses: codecov/codecov-action@v3
      with:
        file: ./server/coverage/lcov.info
        flags: backend
        name: esus-audit-ai-backend

    - name: 'Build application'
      run: |
        cd server
        npm run build

  # Security Scan Job
  security-scan:
    runs-on: ubuntu-latest
    needs: test-and-build
    steps:
    - name: 'Checkout GitHub Action'
      uses: actions/checkout@v4

    - name: 'Run npm audit'
      run: |
        cd server
        npm audit --audit-level moderate

    - name: 'Run Snyk security scan'
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --severity-threshold=high
        command: test

  # Deploy to Development
  deploy-dev:
    runs-on: ubuntu-latest
    needs: [test-and-build, security-scan]
    if: github.ref == 'refs/heads/develop'
    environment: development
    steps:
    - name: 'Checkout GitHub Action'
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: 'Install dependencies'
      run: |
        cd server
        npm ci --only=production

    - name: 'Deploy to Azure Functions (Development)'
      uses: Azure/functions-action@v1
      with:
        app-name: 'esus-audit-ai-functions-dev'
        package: ${{ env.AZURE_FUNCTIONAPP_PACKAGE_PATH }}
        publish-profile: ${{ secrets.AZURE_FUNCTIONAPP_PUBLISH_PROFILE_DEV }}

    - name: 'Run health check'
      run: |
        sleep 30
        curl -f https://esus-audit-ai-functions-dev.azurewebsites.net/api/health || exit 1

  # Deploy to Staging
  deploy-staging:
    runs-on: ubuntu-latest
    needs: [test-and-build, security-scan]
    if: github.ref == 'refs/heads/staging'
    environment: staging
    steps:
    - name: 'Checkout GitHub Action'
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: 'Install dependencies'
      run: |
        cd server
        npm ci --only=production

    - name: 'Deploy to Azure Functions (Staging)'
      uses: Azure/functions-action@v1
      with:
        app-name: 'esus-audit-ai-functions-staging'
        package: ${{ env.AZURE_FUNCTIONAPP_PACKAGE_PATH }}
        publish-profile: ${{ secrets.AZURE_FUNCTIONAPP_PUBLISH_PROFILE_STAGING }}

    - name: 'Run integration tests'
      run: |
        cd server
        npm run test:integration
      env:
        API_BASE_URL: https://esus-audit-ai-functions-staging.azurewebsites.net/api

    - name: 'Run health check'
      run: |
        sleep 30
        curl -f https://esus-audit-ai-functions-staging.azurewebsites.net/api/health || exit 1

  # Deploy to Production
  deploy-production:
    runs-on: ubuntu-latest
    needs: [test-and-build, security-scan]
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
    - name: 'Checkout GitHub Action'
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: 'Install dependencies'
      run: |
        cd server
        npm ci --only=production

    - name: 'Deploy to Azure Functions (Production)'
      uses: Azure/functions-action@v1
      with:
        app-name: 'esus-audit-ai-functions-prod'
        package: ${{ env.AZURE_FUNCTIONAPP_PACKAGE_PATH }}
        publish-profile: ${{ secrets.AZURE_FUNCTIONAPP_PUBLISH_PROFILE_PROD }}

    - name: 'Run smoke tests'
      run: |
        sleep 60
        curl -f https://esus-audit-ai-functions-prod.azurewebsites.net/api/health || exit 1

    - name: 'Notify deployment success'
      uses: 8398a7/action-slack@v3
      with:
        status: success
        text: 'Esus Audit AI successfully deployed to production!'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      if: success()

    - name: 'Notify deployment failure'
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        text: 'Esus Audit AI production deployment failed!'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      if: failure()

  # Rollback job (manual trigger)
  rollback:
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch'
    environment: production
    steps:
    - name: 'Rollback production deployment'
      run: |
        echo "Manual rollback triggered"
        # Add rollback logic here
