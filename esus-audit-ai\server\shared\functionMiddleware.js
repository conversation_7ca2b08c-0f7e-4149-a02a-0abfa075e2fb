// Azure Functions middleware for enhanced error handling and logging
const logger = require('./logger');
const errorHandler = require('./errorHandler');
const monitoring = require('./monitoring');

class FunctionMiddleware {
    // Wrapper for Azure Functions with comprehensive error handling and logging
    static withErrorHandling(handler) {
        return async (request, context) => {
            const startTime = Date.now();
            let correlationId;

            try {
                // Log incoming request
                correlationId = logger.logRequest(request, {
                    functionName: context.functionName,
                    invocationId: context.invocationId
                }, context);

                // Add correlation ID to context for downstream use
                context.correlationId = correlationId;
                request.correlationId = correlationId;

                // Execute the actual function
                const result = await handler(request, context);

                // Log successful response
                const duration = Date.now() - startTime;
                logger.logResponse(correlationId, result.status || 200, duration, {
                    functionName: context.functionName,
                    invocationId: context.invocationId
                }, context);

                // Track performance metrics
                monitoring.trackPerformanceMetric(`Function.${context.functionName}`, duration, {
                    success: 'true',
                    statusCode: (result.status || 200).toString(),
                    correlationId
                });

                return result;

            } catch (error) {
                const duration = Date.now() - startTime;
                
                // Handle the error using our error handler
                const errorResponse = errorHandler.handleError(error, {
                    functionName: context.functionName,
                    invocationId: context.invocationId,
                    correlationId: correlationId || logger.generateCorrelationId(),
                    method: request.method,
                    url: request.url,
                    userAgent: request.headers?.['user-agent'],
                    userId: request.user?.id
                });

                // Log error response
                logger.logResponse(
                    errorResponse.error.correlationId, 
                    errorResponse.statusCode, 
                    duration, 
                    {
                        functionName: context.functionName,
                        invocationId: context.invocationId,
                        error: true
                    }, 
                    context
                );

                // Track error metrics
                monitoring.trackPerformanceMetric(`Function.${context.functionName}`, duration, {
                    success: 'false',
                    statusCode: errorResponse.statusCode.toString(),
                    errorCode: errorResponse.error.code,
                    correlationId: errorResponse.error.correlationId
                });

                return {
                    status: errorResponse.statusCode,
                    jsonBody: errorResponse.error,
                    headers: {
                        'X-Correlation-ID': errorResponse.error.correlationId,
                        'Content-Type': 'application/json'
                    }
                };
            }
        };
    }

    // Authentication middleware
    static withAuth(handler, options = {}) {
        return FunctionMiddleware.withErrorHandling(async (request, context) => {
            const authService = require('./auth');
            
            try {
                // Extract token from Authorization header
                const authHeader = request.headers.authorization;
                if (!authHeader || !authHeader.startsWith('Bearer ')) {
                    throw new Error('Missing or invalid authorization header');
                }

                const token = authHeader.substring(7);
                const user = authService.verifyToken(token);
                
                // Add user to request context
                request.user = user;
                
                // Log authentication success
                logger.info('User authenticated successfully', {
                    userId: user.id,
                    email: user.email,
                    role: user.role
                }, context);

                return await handler(request, context);

            } catch (error) {
                logger.logSecurityEvent('Authentication Failed', null, request.headers?.['x-forwarded-for'], {
                    userAgent: request.headers?.['user-agent'],
                    authHeader: request.headers.authorization ? '[REDACTED]' : 'missing'
                }, context);

                throw new Error('Authentication failed');
            }
        });
    }

    // Validation middleware
    static withValidation(schema) {
        return (handler) => {
            return FunctionMiddleware.withErrorHandling(async (request, context) => {
                try {
                    // Basic validation - you can enhance this with a proper validation library
                    if (schema.body && request.method !== 'GET') {
                        const body = await request.json();
                        request.body = body;
                        
                        // Validate required fields
                        if (schema.body.required) {
                            for (const field of schema.body.required) {
                                if (!body[field]) {
                                    throw new Error(`Missing required field: ${field}`);
                                }
                            }
                        }
                    }

                    if (schema.query) {
                        const query = request.query;
                        
                        // Validate required query parameters
                        if (schema.query.required) {
                            for (const param of schema.query.required) {
                                if (!query[param]) {
                                    throw new Error(`Missing required query parameter: ${param}`);
                                }
                            }
                        }
                    }

                    return await handler(request, context);

                } catch (error) {
                    logger.warn('Request validation failed', {
                        error: error.message,
                        method: request.method,
                        url: request.url
                    }, context);

                    throw error;
                }
            });
        };
    }

    // Rate limiting middleware (basic implementation)
    static withRateLimit(options = { maxRequests: 100, windowMs: 60000 }) {
        const requests = new Map();

        return (handler) => {
            return FunctionMiddleware.withErrorHandling(async (request, context) => {
                const clientIP = request.headers['x-forwarded-for'] || 'unknown';
                const now = Date.now();
                const windowStart = now - options.windowMs;

                // Clean old entries
                for (const [ip, timestamps] of requests.entries()) {
                    const validTimestamps = timestamps.filter(t => t > windowStart);
                    if (validTimestamps.length === 0) {
                        requests.delete(ip);
                    } else {
                        requests.set(ip, validTimestamps);
                    }
                }

                // Check current IP
                const clientRequests = requests.get(clientIP) || [];
                const recentRequests = clientRequests.filter(t => t > windowStart);

                if (recentRequests.length >= options.maxRequests) {
                    logger.logSecurityEvent('Rate Limit Exceeded', request.user?.id, clientIP, {
                        requestCount: recentRequests.length,
                        maxRequests: options.maxRequests,
                        windowMs: options.windowMs
                    }, context);

                    throw new Error('Rate limit exceeded');
                }

                // Add current request
                recentRequests.push(now);
                requests.set(clientIP, recentRequests);

                return await handler(request, context);
            });
        };
    }

    // CORS middleware
    static withCORS(options = {}) {
        const defaultOptions = {
            allowedOrigins: process.env.ALLOWED_ORIGINS?.split(',') || ['*'],
            allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-Correlation-ID'],
            allowCredentials: true
        };

        const corsOptions = { ...defaultOptions, ...options };

        return (handler) => {
            return FunctionMiddleware.withErrorHandling(async (request, context) => {
                // Handle preflight requests
                if (request.method === 'OPTIONS') {
                    return {
                        status: 200,
                        headers: {
                            'Access-Control-Allow-Origin': corsOptions.allowedOrigins.join(','),
                            'Access-Control-Allow-Methods': corsOptions.allowedMethods.join(','),
                            'Access-Control-Allow-Headers': corsOptions.allowedHeaders.join(','),
                            'Access-Control-Allow-Credentials': corsOptions.allowCredentials.toString()
                        }
                    };
                }

                const result = await handler(request, context);

                // Add CORS headers to response
                result.headers = {
                    ...result.headers,
                    'Access-Control-Allow-Origin': corsOptions.allowedOrigins.join(','),
                    'Access-Control-Allow-Credentials': corsOptions.allowCredentials.toString()
                };

                return result;
            });
        };
    }

    // Compose multiple middlewares
    static compose(...middlewares) {
        return (handler) => {
            return middlewares.reduceRight((acc, middleware) => {
                return middleware(acc);
            }, handler);
        };
    }
}

module.exports = FunctionMiddleware;
