# Esus Audit AI - Azure Functions Backend

## Overview

Production-ready Azure Functions backend for Esus Audit AI with enhanced logging, comprehensive error handling, and automated CI/CD pipeline.

## 🚀 Quick Start

### Prerequisites
- Node.js 18.x or later
- Azure Functions Core Tools v4.x
- Azure CLI (for deployment)

### Development Setup
```bash
# Install dependencies
npm install

# Copy environment configuration
cp local.settings.json.example local.settings.json

# Configure your local.settings.json with actual values

# Start development server
npm run dev

# Run with verbose logging
npm run dev:watch
```

## 📁 Project Structure

```
server/
├── src/
│   ├── app.js                 # Main application entry
│   └── functions/             # Azure Functions
│       ├── health.js          # Enhanced health monitoring
│       ├── auth.js            # Authentication with middleware
│       ├── uploadDoc.js       # Document upload
│       ├── analyzeDoc.js      # Document analysis
│       ├── generateReport.js  # Report generation
│       └── askEsus.js         # AI chat functionality
├── shared/                    # Shared utilities
│   ├── logger.js              # Enhanced structured logging
│   ├── errorHandler.js        # Comprehensive error handling
│   ├── monitoring.js          # Application Insights integration
│   ├── functionMiddleware.js  # Azure Functions middleware
│   ├── auth.js                # Authentication service
│   ├── database.js            # Database connection
│   └── config.js              # Configuration management
├── scripts/
│   └── deploy.ps1             # PowerShell deployment script
├── docs/
│   └── DEPLOYMENT.md          # Detailed deployment guide
├── host.json                  # Azure Functions host configuration
├── host.production.json       # Production-specific configuration
└── package.json               # Dependencies and scripts
```

## 🛠️ Available Scripts

```bash
# Development
npm run dev              # Start Azure Functions locally
npm run dev:watch        # Start with verbose logging

# Quality Assurance
npm run lint             # Run ESLint
npm run lint:fix         # Fix ESLint issues automatically
npm run test             # Run unit tests
npm run test:watch       # Run tests in watch mode
npm run test:coverage    # Run tests with coverage report
npm run test:integration # Run integration tests

# Build & Deployment
npm run build            # Build application (runs tests + lint)
npm run deploy:dev       # Deploy to development environment
npm run deploy:staging   # Deploy to staging environment
npm run deploy:prod      # Deploy to production environment

# Database
npm run db:setup         # Setup database schema
npm run db:test          # Test database connection
```

## 🔧 Configuration

### Environment Variables

#### Required for All Environments
```env
# Azure Functions
FUNCTIONS_WORKER_RUNTIME=node
FUNCTIONS_EXTENSION_VERSION=~4

# Database
DATABASE_URL=******************************************/database

# Azure Services
AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;...
AZURE_FORM_RECOGNIZER_ENDPOINT=https://your-service.cognitiveservices.azure.com/
AZURE_FORM_RECOGNIZER_KEY=your_key_here
AZURE_OPENAI_ENDPOINT=https://your-openai.openai.azure.com/
AZURE_OPENAI_KEY=your_key_here
AZURE_SEARCH_ENDPOINT=https://your-search.search.windows.net
AZURE_SEARCH_KEY=your_key_here

# Application Insights
APPLICATIONINSIGHTS_CONNECTION_STRING=InstrumentationKey=...

# Security
JWT_SECRET=your_secure_jwt_secret_here
```

#### Optional Configuration
```env
# Logging
LOG_LEVEL=info                    # error, warn, info, debug

# Application Settings
NODE_ENV=development              # development, staging, production
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173
MAX_FILE_SIZE=52428800           # 50MB in bytes
ALLOWED_FILE_TYPES=.pdf,.xlsx,.xls,.docx,.doc,.csv
```

## 🏗️ Architecture Features

### Enhanced Logging
- **Structured JSON logging** with correlation IDs
- **Azure Functions context** integration
- **Application Insights** automatic telemetry
- **Security event logging** for audit trails
- **Performance metrics** tracking

### Comprehensive Error Handling
- **Centralized error processing** with proper HTTP status codes
- **Operational vs programming error** classification
- **Automatic retry logic** for transient failures
- **Error frequency tracking** and alerting
- **User-friendly error messages**

### Middleware System
- **Authentication middleware** with JWT validation
- **Request validation** with schema enforcement
- **Rate limiting** to prevent abuse
- **CORS handling** for cross-origin requests
- **Error wrapping** for consistent responses

### Monitoring & Observability
- **Health checks** with dependency validation
- **Performance tracking** for all operations
- **Custom metrics** for business events
- **Distributed tracing** with correlation IDs
- **Real-time monitoring** via Application Insights

## 🚀 Deployment

### Automated CI/CD (Recommended)
The project includes GitHub Actions workflow for automated deployment:

- **Development**: Push to `develop` branch
- **Staging**: Push to `staging` branch  
- **Production**: Push to `main` branch

### Manual Deployment
```powershell
# Using PowerShell script
.\scripts\deploy.ps1 -Environment prod

# Using Azure Functions Core Tools
func azure functionapp publish esus-audit-ai-functions-prod
```

## 📊 Monitoring

### Health Check Endpoint
```
GET /api/health
```

Returns comprehensive system status including:
- Database connectivity
- Memory usage
- Configuration validation
- Azure services status

### Application Insights Integration
- Request/response tracking
- Dependency monitoring
- Exception tracking
- Custom events and metrics
- Performance counters

## 🔒 Security Features

### Authentication
- JWT-based authentication
- Secure password hashing with bcrypt
- Token expiration and validation
- Rate limiting on auth endpoints

### Security Logging
- Failed login attempts
- Suspicious activity detection
- IP address tracking
- User action auditing

### Data Protection
- Sensitive header sanitization
- SQL injection prevention
- Input validation and sanitization
- CORS configuration

## 🧪 Testing

### Unit Tests
```bash
npm run test
```

### Integration Tests
```bash
npm run test:integration
```

### Coverage Reports
```bash
npm run test:coverage
```

## 📚 API Documentation

### Authentication Endpoints
- `POST /api/login` - User login
- `POST /api/register` - User registration
- `GET /api/verify` - Token verification

### Document Processing
- `POST /api/uploadDoc` - Upload document
- `POST /api/analyzeDoc` - Analyze document
- `POST /api/generateReport` - Generate audit report

### AI Features
- `POST /api/askEsus` - AI chat functionality

### System
- `GET /api/health` - System health check

## 🤝 Contributing

1. Follow the existing code style
2. Add tests for new features
3. Update documentation
4. Ensure all checks pass before submitting PR

## 📄 License

This project is proprietary software for Esus Audit AI.

## 🆘 Support

For deployment issues, check:
1. [Deployment Guide](./docs/DEPLOYMENT.md)
2. Application Insights logs
3. Azure Function App logs
4. Health check endpoint status

For development support, contact the development team.
