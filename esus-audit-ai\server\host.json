{"version": "2.0", "logging": {"applicationInsights": {"samplingSettings": {"isEnabled": true, "maxTelemetryItemsPerSecond": 20, "excludedTypes": "Request;Exception"}, "enableLiveMetricsFilters": true, "httpAutoCollectionOptions": {"enableHttpTriggerExtendedInfoCollection": true, "enableW3CDistributedTracing": true, "enableResponseHeaderInjection": true}}, "logLevel": {"default": "Information", "Host.Results": "Information", "Function": "Information", "Host.Aggregator": "Information"}, "console": {"isEnabled": true}, "fileLoggingMode": "always"}, "extensionBundle": {"id": "Microsoft.Azure.Functions.ExtensionBundle", "version": "[4.*, 5.0.0)"}, "functionTimeout": "00:10:00", "healthMonitor": {"enabled": true, "healthCheckInterval": "00:00:10", "healthCheckWindow": "00:02:00", "healthCheckThreshold": 6, "counterThreshold": 0.8}, "http": {"routePrefix": "api", "maxOutstandingRequests": 200, "maxConcurrentRequests": 100, "dynamicThrottlesEnabled": true, "hsts": {"isEnabled": true, "maxAge": "365"}}, "retry": {"strategy": "exponentialBackoff", "maxRetryCount": 3, "minimumInterval": "00:00:02", "maximumInterval": "00:00:30"}, "cors": {"allowedOrigins": ["http://localhost:3000", "http://localhost:5173"], "allowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "allowedHeaders": ["Content-Type", "Authorization", "X-Correlation-ID"], "allowCredentials": true}}