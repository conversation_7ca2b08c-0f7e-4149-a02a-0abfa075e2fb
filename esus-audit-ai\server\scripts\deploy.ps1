# PowerShell deployment script for Esus Audit AI Azure Functions
param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "staging", "prod")]
    [string]$Environment,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipTests,
    
    [Parameter(Mandatory=$false)]
    [switch]$Force
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Configuration
$AppNames = @{
    "dev" = "esus-audit-ai-functions-dev"
    "staging" = "esus-audit-ai-functions-staging"
    "prod" = "esus-audit-ai-functions-prod"
}

$ResourceGroups = @{
    "dev" = "esus-audit-ai-dev-rg"
    "staging" = "esus-audit-ai-staging-rg"
    "prod" = "esus-audit-ai-prod-rg"
}

Write-Host "🚀 Starting deployment to $Environment environment..." -ForegroundColor Green

# Validate Azure CLI is installed and logged in
try {
    $azAccount = az account show --query "name" -o tsv
    Write-Host "✅ Logged in to Azure as: $azAccount" -ForegroundColor Green
} catch {
    Write-Error "❌ Azure CLI not installed or not logged in. Please run 'az login' first."
    exit 1
}

# Validate Function Core Tools
try {
    $funcVersion = func --version
    Write-Host "✅ Azure Functions Core Tools version: $funcVersion" -ForegroundColor Green
} catch {
    Write-Error "❌ Azure Functions Core Tools not installed. Please install from https://docs.microsoft.com/en-us/azure/azure-functions/functions-run-local"
    exit 1
}

# Change to server directory
Set-Location $PSScriptRoot\..

# Install dependencies
Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
npm ci

# Run linting
if (-not $SkipTests) {
    Write-Host "🔍 Running linting..." -ForegroundColor Yellow
    npm run lint
    if ($LASTEXITCODE -ne 0) {
        Write-Error "❌ Linting failed"
        exit 1
    }
}

# Run tests
if (-not $SkipTests) {
    Write-Host "🧪 Running tests..." -ForegroundColor Yellow
    npm run test
    if ($LASTEXITCODE -ne 0) {
        Write-Error "❌ Tests failed"
        exit 1
    }
}

# Build application
Write-Host "🔨 Building application..." -ForegroundColor Yellow
npm run build
if ($LASTEXITCODE -ne 0) {
    Write-Error "❌ Build failed"
    exit 1
}

# Copy production host.json if deploying to production
if ($Environment -eq "prod") {
    Write-Host "📋 Using production host.json configuration..." -ForegroundColor Yellow
    Copy-Item "host.production.json" "host.json" -Force
}

# Confirm deployment for production
if ($Environment -eq "prod" -and -not $Force) {
    $confirmation = Read-Host "⚠️  You are about to deploy to PRODUCTION. Type 'DEPLOY' to confirm"
    if ($confirmation -ne "DEPLOY") {
        Write-Host "❌ Deployment cancelled" -ForegroundColor Red
        exit 1
    }
}

# Deploy to Azure Functions
$appName = $AppNames[$Environment]
$resourceGroup = $ResourceGroups[$Environment]

Write-Host "🚀 Deploying to Azure Functions: $appName..." -ForegroundColor Green

try {
    func azure functionapp publish $appName --build remote --force
    if ($LASTEXITCODE -ne 0) {
        throw "Deployment failed"
    }
} catch {
    Write-Error "❌ Deployment failed: $_"
    exit 1
}

# Wait for deployment to complete
Write-Host "⏳ Waiting for deployment to complete..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Health check
Write-Host "🏥 Running health check..." -ForegroundColor Yellow
$healthUrl = "https://$appName.azurewebsites.net/api/health"

try {
    $response = Invoke-RestMethod -Uri $healthUrl -Method Get -TimeoutSec 30
    if ($response.status -eq "healthy") {
        Write-Host "✅ Health check passed!" -ForegroundColor Green
        Write-Host "📊 Response time: $($response.responseTime)ms" -ForegroundColor Green
    } else {
        Write-Warning "⚠️  Health check returned: $($response.status)"
        Write-Host "Response: $($response | ConvertTo-Json -Depth 3)" -ForegroundColor Yellow
    }
} catch {
    Write-Error "❌ Health check failed: $_"
    exit 1
}

# Show deployment summary
Write-Host "`n🎉 Deployment completed successfully!" -ForegroundColor Green
Write-Host "Environment: $Environment" -ForegroundColor Cyan
Write-Host "Function App: $appName" -ForegroundColor Cyan
Write-Host "Health Check URL: $healthUrl" -ForegroundColor Cyan
Write-Host "Function App URL: https://$appName.azurewebsites.net" -ForegroundColor Cyan

# Restore original host.json if we modified it
if ($Environment -eq "prod" -and (Test-Path "host.json.bak")) {
    Move-Item "host.json.bak" "host.json" -Force
}

Write-Host "`n✨ Deployment script completed!" -ForegroundColor Green
